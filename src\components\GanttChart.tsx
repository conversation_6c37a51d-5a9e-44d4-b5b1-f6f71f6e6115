import { useState, useMemo } from 'react';
import { format, startOfWeek, endOfWeek, eachDayOfInterval, addDays, parseISO, differenceInDays, isWithinInterval, startOfMonth, endOfMonth } from 'date-fns';
import type { Task, Project } from '../types.js';

interface GanttChartProps {
  tasks: Task[];
  projects: Project[];
}

interface GanttTask {
  id: number;
  title: string;
  startDate: Date;
  endDate: Date;
  progress: number;
  project?: Project;
  priority: string;
  completed: boolean;
}

export default function GanttChart({ tasks, projects }: GanttChartProps) {
  const [selectedProject, setSelectedProject] = useState<number | 'all'>('all');
  const [viewMode, setViewMode] = useState<'week' | 'month'>('week');

  // Filter and transform tasks for Gantt display
  const ganttTasks = useMemo(() => {
    const filteredTasks = tasks.filter(task => {
      // Only show tasks with due dates and not subtasks
      if (!task.dueDate || task.parentTaskId) return false;
      
      // Filter by selected project
      if (selectedProject !== 'all' && task.projectId !== selectedProject) return false;
      
      return true;
    });

    return filteredTasks.map(task => {
      const dueDate = parseISO(task.dueDate!);
      const createdDate = parseISO(task.createdAt);
      
      // Calculate start date (created date or 7 days before due date, whichever is later)
      const defaultStartDate = addDays(dueDate, -7);
      const startDate = createdDate > defaultStartDate ? createdDate : defaultStartDate;
      
      return {
        id: task.id,
        title: task.title,
        startDate,
        endDate: dueDate,
        progress: task.completed ? 100 : 0,
        project: projects.find(p => p.id === task.projectId),
        priority: task.priority,
        completed: task.completed,
      };
    }).sort((a, b) => a.startDate.getTime() - b.startDate.getTime());
  }, [tasks, projects, selectedProject]);

  // Calculate date range for the timeline
  const dateRange = useMemo(() => {
    if (ganttTasks.length === 0) {
      const today = new Date();
      if (viewMode === 'week') {
        return {
          start: startOfWeek(today),
          end: endOfWeek(addDays(today, 14)), // 2 weeks for week view
        };
      } else {
        return {
          start: startOfWeek(today),
          end: endOfWeek(addDays(today, 60)), // 2 months for month view
        };
      }
    }

    const allDates = ganttTasks.flatMap(task => [task.startDate, task.endDate]);
    const minDate = new Date(Math.min(...allDates.map(d => d.getTime())));
    const maxDate = new Date(Math.max(...allDates.map(d => d.getTime())));

    if (viewMode === 'week') {
      return {
        start: startOfWeek(addDays(minDate, -3)),
        end: endOfWeek(addDays(maxDate, 3)),
      };
    } else {
      return {
        start: startOfWeek(addDays(minDate, -14)),
        end: endOfWeek(addDays(maxDate, 14)),
      };
    }
  }, [ganttTasks, viewMode]);

  // Generate timeline days
  const timelineDays = useMemo(() => {
    return eachDayOfInterval(dateRange);
  }, [dateRange]);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'HIGH': return '#ef4444';
      case 'MEDIUM': return '#f59e0b';
      case 'LOW': return '#10b981';
      default: return '#6b7280';
    }
  };

  const calculateTaskPosition = (task: GanttTask) => {
    const totalDays = differenceInDays(dateRange.end, dateRange.start);
    const startOffset = differenceInDays(task.startDate, dateRange.start);
    const duration = differenceInDays(task.endDate, task.startDate) + 1;

    return {
      left: `${(startOffset / totalDays) * 100}%`,
      width: `${(duration / totalDays) * 100}%`,
    };
  };

  return (
    <div className="gantt-chart">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-primary">Gantt Charts</h1>
          <p className="text-secondary mt-1">Project timeline visualization</p>
        </div>
        
        <div className="flex items-center space-x-4">
          {/* Project Filter */}
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-secondary">Project:</label>
            <select
              value={selectedProject}
              onChange={(e) => setSelectedProject(e.target.value === 'all' ? 'all' : parseInt(e.target.value))}
              className="input-field text-sm"
            >
              <option value="all">All Projects</option>
              {projects.map(project => (
                <option key={project.id} value={project.id}>
                  {project.name}
                </option>
              ))}
            </select>
          </div>

          {/* View Mode */}
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-secondary">View:</label>
            <div className="flex rounded-lg border border-border overflow-hidden">
              <button
                onClick={() => setViewMode('week')}
                className={`px-3 py-1 text-sm ${
                  viewMode === 'week' 
                    ? 'bg-primary text-white' 
                    : 'bg-surface text-secondary hover:bg-surface-hover'
                }`}
              >
                Week
              </button>
              <button
                onClick={() => setViewMode('month')}
                className={`px-3 py-1 text-sm ${
                  viewMode === 'month' 
                    ? 'bg-primary text-white' 
                    : 'bg-surface text-secondary hover:bg-surface-hover'
                }`}
              >
                Month
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Gantt Chart Container */}
      <div className="gantt-container bg-white rounded-lg border border-border overflow-hidden">
        {ganttTasks.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12">
            <svg className="w-16 h-16 text-muted mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            <h3 className="text-lg font-medium text-primary mb-2">No Timeline Data</h3>
            <p className="text-secondary text-center max-w-md">
              {selectedProject === 'all' 
                ? "Add tasks with due dates to see them in the timeline view."
                : "No tasks with due dates found for the selected project."
              }
            </p>
          </div>
        ) : (
          <>
            {/* Timeline Header */}
            <div className="gantt-header border-b border-border">
              <div className="flex">
                {/* Task Names Column */}
                <div className="gantt-task-column bg-surface border-r border-border">
                  <div className="gantt-header-cell">
                    <span className="font-medium text-primary">Tasks</span>
                  </div>
                </div>
                
                {/* Timeline Dates */}
                <div className="gantt-timeline-header flex-1">
                  <div className="flex">
                    {timelineDays.map((day, index) => (
                      <div
                        key={index}
                        className="gantt-date-cell border-r border-border last:border-r-0"
                        style={{
                          minWidth: viewMode === 'week' ? '50px' : '30px',
                          flex: '1'
                        }}
                      >
                        <div className="text-xs font-medium text-secondary">
                          {viewMode === 'week' ? format(day, 'MMM d') : format(day, 'd')}
                        </div>
                        <div className="text-xs text-muted">
                          {viewMode === 'week' ? format(day, 'EEE') : format(day, 'E')}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Gantt Body */}
            <div className="gantt-body">
              {ganttTasks.map((task, index) => (
                <div key={task.id} className="gantt-row border-b border-border last:border-b-0">
                  <div className="flex">
                    {/* Task Name */}
                    <div className="gantt-task-column bg-surface border-r border-border">
                      <div className="gantt-task-cell">
                        <div className="flex items-center space-x-2">
                          {task.project && (
                            <div
                              className="w-3 h-3 rounded-full flex-shrink-0"
                              style={{ backgroundColor: task.project.color }}
                            />
                          )}
                          <div className="flex-1 min-w-0">
                            <div className={`text-sm font-medium truncate ${
                              task.completed ? 'line-through text-tertiary' : 'text-primary'
                            }`}>
                              {task.title}
                            </div>
                            <div className="text-xs text-muted">
                              {format(task.startDate, 'MMM d')} - {format(task.endDate, 'MMM d')}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Timeline */}
                    <div className="gantt-timeline flex-1 relative">
                      <div
                        className="gantt-task-bar absolute top-1/2 transform -translate-y-1/2 rounded"
                        style={{
                          ...calculateTaskPosition(task),
                          backgroundColor: task.completed ? '#10b981' : getPriorityColor(task.priority),
                          opacity: task.completed ? 0.7 : 0.8,
                          height: '20px',
                          minWidth: '20px',
                        }}
                      >
                        <div className="gantt-task-progress h-full rounded" 
                             style={{ 
                               width: `${task.progress}%`, 
                               backgroundColor: 'rgba(255, 255, 255, 0.3)' 
                             }} 
                        />
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </>
        )}
      </div>

      {/* Legend */}
      <div className="mt-6 flex items-center justify-center text-sm" style={{ gap: '10px' }}>
        <div className="flex items-center" style={{ gap: '5px' }}>
          <div className="w-4 h-4 rounded" style={{ backgroundColor: '#ef4444' }}></div>
          <span className="text-secondary">High Priority</span>
        </div>
        <div className="flex items-center" style={{ gap: '5px' }}>
          <div className="w-4 h-4 rounded" style={{ backgroundColor: '#f59e0b' }}></div>
          <span className="text-secondary">Medium Priority</span>
        </div>
        <div className="flex items-center" style={{ gap: '5px' }}>
          <div className="w-4 h-4 rounded" style={{ backgroundColor: '#10b981' }}></div>
          <span className="text-secondary">Low Priority / Completed</span>
        </div>
      </div>
    </div>
  );
}
