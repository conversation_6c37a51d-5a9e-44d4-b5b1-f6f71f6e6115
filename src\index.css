@tailwind base;
@tailwind components;
@tailwind utilities;

/* Clean Professional Color System - Based on Reference Design */
:root {
  /* Primary Colors - Blue theme like reference */
  --primary: #2563eb;
  --primary-hover: #1d4ed8;
  --primary-light: #dbeafe;
  --primary-dark: #1e40af;

  /* Neutral Colors */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Surface Colors */
  --surface: #ffffff;
  --surface-secondary: #f8fafc;
  --surface-tertiary: #f1f5f9;
  --border: #e2e8f0;
  --border-light: #f1f5f9;

  /* Text Colors */
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-tertiary: #64748b;
  --text-muted: #94a3b8;

  /* Status Colors */
  --success: #10b981;
  --success-light: #d1fae5;
  --warning: #f59e0b;
  --warning-light: #fef3c7;
  --error: #ef4444;
  --error-light: #fee2e2;

  /* Shadows - Subtle like reference */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
  --radius-xl: 12px;
}

/* Clean Professional Base Styles */
@layer base {
  * {
    box-sizing: border-box;
  }

  body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--surface-secondary);
    margin: 0;
    padding: 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    margin: 0;
    color: var(--text-primary);
  }

  h1 { font-size: 2rem; }
  h2 { font-size: 1.5rem; }
  h3 { font-size: 1.25rem; }
  h4 { font-size: 1.125rem; }
  h5 { font-size: 1rem; }
  h6 { font-size: 0.875rem; }

  p {
    margin: 0;
    line-height: 1.6;
    color: var(--text-secondary);
  }

  html {
    scroll-behavior: smooth;
  }

  /* Clean Focus Styles */
  *:focus-visible {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
    border-radius: var(--radius-sm);
  }
}

/* Clean Professional Components */
@layer components {
  /* Button Styles - Clean like reference */
  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    border-radius: var(--radius-md);
    border: 1px solid transparent;
    cursor: pointer;
    transition: all 0.15s ease;
    text-decoration: none;
    min-height: 36px;
  }

  .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  /* Primary Button */
  .btn-primary {
    background-color: var(--primary);
    color: white;
    border-color: var(--primary);
  }

  .btn-primary:hover:not(:disabled) {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
  }

  /* Secondary Button */
  .btn-secondary {
    background-color: var(--surface);
    color: var(--text-secondary);
    border-color: var(--border);
  }

  .btn-secondary:hover:not(:disabled) {
    background-color: var(--surface-tertiary);
    border-color: var(--gray-300);
  }

  /* Text Button */
  .btn-text {
    background-color: transparent;
    color: var(--text-secondary);
    border: none;
    padding: 6px 12px;
  }

  .btn-text:hover:not(:disabled) {
    background-color: var(--surface-tertiary);
    color: var(--text-primary);
  }

  /* Form Fields - Clean like reference */
  .form-field {
    margin-bottom: 20px;
  }

  .form-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 6px;
  }

  .form-input, .form-select, .form-textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    background-color: var(--surface);
    font-size: 14px;
    line-height: 20px;
    color: var(--text-primary);
    transition: all 0.15s ease;
  }

  .form-input:focus, .form-select:focus, .form-textarea:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }

  .form-input::placeholder, .form-textarea::placeholder {
    color: var(--text-muted);
  }

  .form-textarea {
    resize: vertical;
    min-height: 80px;
  }

  .form-select {
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 8px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 32px;
    appearance: none;
  }

  /* Cards - Clean like reference */
  .card {
    background-color: var(--surface);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border);
    transition: all 0.15s ease;
    overflow: hidden;
  }

  .card-hover:hover {
    border-color: var(--gray-300);
    box-shadow: var(--shadow-sm);
  }

  /* Stats Cards */
  .stats-card {
    background-color: var(--surface);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border);
    padding: 20px;
    transition: all 0.15s ease;
    position: relative;
  }

  .stats-card:hover {
    border-color: var(--gray-300);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
  }

  /* Task Cards */
  .task-card {
    background-color: var(--surface);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border);
    padding: 16px;
    transition: all 0.15s ease;
  }

  .task-card:hover {
    border-color: var(--gray-300);
    box-shadow: var(--shadow-sm);
  }

  .task-card.completed {
    background-color: var(--surface-tertiary);
    border-color: var(--border-light);
  }

  .task-card.overdue {
    border-color: var(--error);
    background-color: var(--error-light);
  }

  /* Badges/Chips - Clean like reference */
  .badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-size: 12px;
    font-weight: 500;
    line-height: 16px;
  }

  /* Priority Badges */
  .badge-low {
    background-color: var(--success-light);
    color: var(--success);
  }

  .badge-medium {
    background-color: var(--warning-light);
    color: var(--warning);
  }

  .badge-high {
    background-color: #fef3c7;
    color: #d97706;
  }

  .badge-urgent {
    background-color: var(--error-light);
    color: var(--error);
  }

  /* Status Badges */
  .badge-completed {
    background-color: var(--success-light);
    color: var(--success);
  }

  .badge-pending {
    background-color: var(--gray-100);
    color: var(--gray-600);
  }

  /* Modal/Dialog - Clean like reference */
  .modal-overlay {
    position: fixed;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    animation: fadeIn 0.2s ease;
  }

  .modal {
    background-color: var(--surface);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    overflow: hidden;
    animation: slideUp 0.2s ease;
  }

  .modal-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid var(--border);
  }

  .modal-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
  }

  .modal-content {
    padding: 20px 24px;
    overflow-y: auto;
  }

  .modal-actions {
    padding: 16px 24px 20px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    border-top: 1px solid var(--border);
  }

  /* Loading Spinner */
  .spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border);
    border-top: 3px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  @keyframes spin {
    to { transform: rotate(360deg); }
  }
}

/* Clean Professional Utility Classes */
@layer utilities {
  /* Typography */
  .text-xs { font-size: 12px; line-height: 16px; }
  .text-sm { font-size: 14px; line-height: 20px; }
  .text-base { font-size: 16px; line-height: 24px; }
  .text-lg { font-size: 18px; line-height: 28px; }
  .text-xl { font-size: 20px; line-height: 28px; }
  .text-2xl { font-size: 24px; line-height: 32px; }
  .text-3xl { font-size: 30px; line-height: 36px; }

  /* Font Weights */
  .font-normal { font-weight: 400; }
  .font-medium { font-weight: 500; }
  .font-semibold { font-weight: 600; }
  .font-bold { font-weight: 700; }

  /* Text Colors */
  .text-primary { color: var(--text-primary); }
  .text-secondary { color: var(--text-secondary); }
  .text-tertiary { color: var(--text-tertiary); }
  .text-muted { color: var(--text-muted); }
  .text-success { color: var(--success); }
  .text-warning { color: var(--warning); }
  .text-error { color: var(--error); }

  /* Additional utility classes */
  .bg-primary-light { background-color: var(--primary-light); }
  .bg-success-light { background-color: var(--success-light); }
  .bg-warning-light { background-color: var(--warning-light); }
  .bg-error-light { background-color: var(--error-light); }

  .border-error { border-color: var(--error); }
  .border-border { border-color: var(--border); }

  /* Form input field class */
  .input-field {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    background-color: var(--surface);
    font-size: 14px;
    line-height: 20px;
    color: var(--text-primary);
    transition: all 0.15s ease;
  }

  .input-field:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }

  /* Surface hover */
  .bg-surface-hover { background-color: var(--surface-tertiary); }

  .w-4 { width: 1rem; }
  .w-5 { width: 1.25rem; }
  .w-6 { width: 1.5rem; }
  .w-8 { width: 2rem; }
  .w-12 { width: 3rem; }
  .w-16 { width: 4rem; }

  .h-4 { height: 1rem; }
  .h-5 { height: 1.25rem; }
  .h-6 { height: 1.5rem; }
  .h-8 { height: 2rem; }
  .h-12 { height: 3rem; }
  .h-16 { height: 4rem; }

  .p-1\.5 { padding: 0.375rem; }
  .p-2 { padding: 0.5rem; }
  .p-4 { padding: 1rem; }
  .p-6 { padding: 1.5rem; }
  .p-8 { padding: 2rem; }

  .mb-1 { margin-bottom: 0.25rem; }
  .mb-2 { margin-bottom: 0.5rem; }
  .mb-3 { margin-bottom: 0.75rem; }
  .mb-4 { margin-bottom: 1rem; }
  .mb-6 { margin-bottom: 1.5rem; }
  .mb-8 { margin-bottom: 2rem; }

  .mr-1\.5 { margin-right: 0.375rem; }
  .mr-2 { margin-right: 0.5rem; }
  .ml-3 { margin-left: 0.75rem; }

  .py-1\.5 { padding-top: 0.375rem; padding-bottom: 0.375rem; }
  .px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }

  .space-x-2 > * + * { margin-left: 0.5rem; }
  .space-y-4 > * + * { margin-top: 1rem; }

  .inline { display: inline; }
  .shadow-sm { box-shadow: var(--shadow-sm); }

  .min-h-screen { min-height: 100vh; }
  .max-w-4xl { max-width: 56rem; }
  .max-w-6xl { max-width: 72rem; }
  .mx-auto { margin-left: auto; margin-right: auto; }

  /* Layout Styles */
  .app-layout {
    display: flex;
    min-height: 100vh;
    background-color: var(--surface-secondary);
  }

  .main-content {
    flex: 1;
    transition: margin-left 0.3s ease;
    overflow-x: hidden;
  }

  .main-content-normal {
    margin-left: 280px;
  }

  .main-content-expanded {
    margin-left: 80px;
  }

  .main-content-inner {
    padding: 24px;
    max-width: 1200px;
    margin: 0 auto;
  }

  /* Sidebar Styles */
  .sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    background-color: var(--surface);
    border-right: 1px solid var(--border);
    transition: width 0.3s ease;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    box-shadow: var(--shadow-sm);
  }

  .sidebar-expanded {
    width: 280px;
  }

  .sidebar-collapsed {
    width: 80px;
  }

  .sidebar-header {
    padding: 20px 16px;
    border-bottom: 1px solid var(--border);
    flex-shrink: 0;
  }

  .sidebar-logo {
    flex: 1;
    margin-right: 12px;
  }

  .sidebar-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: none;
    background-color: transparent;
    color: var(--text-secondary);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.15s ease;
    margin-left: auto;
  }

  .sidebar-toggle:hover {
    background-color: var(--surface-tertiary);
    color: var(--text-primary);
  }

  .sidebar-nav {
    flex: 1;
    padding: 16px 0;
    overflow-y: auto;
  }

  .sidebar-nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  .sidebar-nav-item {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 12px 16px;
    border: none;
    background: none;
    color: var(--text-secondary);
    text-align: left;
    cursor: pointer;
    transition: all 0.15s ease;
    border-radius: 0;
    position: relative;
  }

  .sidebar-nav-item:hover {
    background-color: var(--surface-tertiary);
    color: var(--text-primary);
  }

  .sidebar-nav-item.active {
    background-color: var(--primary-light);
    color: var(--primary);
    font-weight: 500;
  }

  .sidebar-nav-item.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background-color: var(--primary);
  }

  .sidebar-nav-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    margin-right: 12px;
    flex-shrink: 0;
  }

  .sidebar-collapsed .sidebar-nav-icon {
    margin-right: 0;
  }

  .sidebar-nav-content {
    flex: 1;
    min-width: 0;
  }

  .sidebar-nav-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
  }

  .sidebar-nav-description {
    display: block;
    font-size: 12px;
    line-height: 16px;
    color: var(--text-muted);
    margin-top: 2px;
  }

  .sidebar-footer {
    padding: 16px;
    border-top: 1px solid var(--border);
    flex-shrink: 0;
  }

  .sidebar-footer-content {
    text-align: center;
  }

  /* Kanban Board Styles */
  .kanban-board {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    min-height: 600px;
  }

  .kanban-column {
    background-color: var(--surface-tertiary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border);
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .kanban-column-header {
    padding: 12px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .kanban-task-count {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 12px;
    font-weight: 500;
    padding: 2px 8px;
    border-radius: 12px;
    min-width: 20px;
    text-align: center;
  }

  .kanban-column-content {
    flex: 1;
    padding: 16px;
    min-height: 500px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    position: relative;
    transition: all 0.2s ease;
  }

  .kanban-column-over {
    background-color: var(--primary-light) !important;
    border: 2px dashed var(--primary) !important;
  }

  .kanban-empty-state {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    border: 2px dashed var(--border);
    border-radius: var(--radius-md);
    background-color: var(--surface);
  }

  .kanban-card {
    background-color: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    cursor: grab;
    transition: all 0.15s ease;
  }

  .kanban-card:hover {
    border-color: var(--gray-300);
    box-shadow: var(--shadow-sm);
    transform: translateY(-1px);
  }

  .kanban-card.dragging {
    opacity: 0.8;
    transform: rotate(5deg);
    box-shadow: var(--shadow-lg);
    cursor: grabbing;
  }

  .kanban-card.overdue {
    border-color: var(--error);
    background-color: var(--error-light);
  }

  .kanban-card-content {
    padding: 12px;
  }

  .kanban-action-btn {
    padding: 4px;
    border-radius: var(--radius-sm);
    background-color: transparent;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    transition: all 0.15s ease;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .kanban-action-btn:hover {
    background-color: var(--surface-tertiary);
    color: var(--text-secondary);
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .max-w-16 { max-width: 4rem; }
  .truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /* Responsive Kanban */
  @media (max-width: 1024px) {
    .kanban-board {
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
    }
  }

  @media (max-width: 640px) {
    .kanban-board {
      grid-template-columns: 1fr;
      gap: 12px;
    }

    .kanban-column-content {
      min-height: 300px;
      padding: 12px;
    }
  }

  /* Background Colors */
  .bg-surface { background-color: var(--surface); }
  .bg-surface-secondary { background-color: var(--surface-secondary); }
  .bg-surface-tertiary { background-color: var(--surface-tertiary); }

  /* Spacing */
  .space-y-1 > * + * { margin-top: 4px; }
  .space-y-2 > * + * { margin-top: 8px; }
  .space-y-3 > * + * { margin-top: 12px; }
  .space-y-4 > * + * { margin-top: 16px; }
  .space-y-6 > * + * { margin-top: 24px; }

  /* Flexbox */
  .flex { display: flex; }
  .flex-col { flex-direction: column; }
  .items-center { align-items: center; }
  .justify-center { justify-content: center; }
  .justify-between { justify-content: space-between; }
  .flex-1 { flex: 1; }

  /* Grid */
  .grid { display: grid; }
  .grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  .gap-4 { gap: 16px; }
  .gap-6 { gap: 24px; }

  /* Misc */
  .rounded { border-radius: var(--radius-md); }
  .rounded-lg { border-radius: var(--radius-lg); }
  .shadow-sm { box-shadow: var(--shadow-sm); }
  .shadow-md { box-shadow: var(--shadow-md); }

  /* Responsive Styles */
  @media (max-width: 768px) {
    .sidebar {
      transform: translateX(-100%);
      transition: transform 0.3s ease;
    }

    .sidebar.sidebar-mobile-open {
      transform: translateX(0);
    }

    .main-content-normal,
    .main-content-expanded {
      margin-left: 0;
    }

    .main-content-inner {
      padding: 16px;
    }

    .kanban-board {
      grid-template-columns: 1fr;
      gap: 16px;
    }

    .grid-cols-2 {
      grid-template-columns: 1fr;
    }

    .grid-cols-4 {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (max-width: 480px) {
    .main-content-inner {
      padding: 12px;
    }

    .sidebar-expanded {
      width: 100vw;
    }
  }

  /* Gantt Chart Styles */
  .gantt-chart {
    padding: 1.5rem;
  }

  .gantt-container {
    min-height: 400px;
    overflow-x: auto;
  }

  .gantt-header {
    background-color: var(--surface);
  }

  .gantt-task-column {
    width: 250px;
    min-width: 250px;
    flex-shrink: 0;
  }

  .gantt-header-cell {
    padding: 1rem;
    font-weight: 600;
    border-bottom: 1px solid var(--border);
  }

  .gantt-date-cell {
    padding: 0.5rem;
    text-align: center;
    background-color: var(--surface);
  }

  .gantt-task-cell {
    padding: 1rem;
    height: 60px;
    display: flex;
    align-items: center;
  }

  .gantt-row {
    min-height: 60px;
  }

  .gantt-timeline {
    position: relative;
    min-height: 60px;
    background: linear-gradient(90deg, transparent 0%, transparent 50%, rgba(0,0,0,0.02) 50%, rgba(0,0,0,0.02) 100%);
    background-size: 60px 100%;
  }

  .gantt-task-bar {
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .gantt-task-bar:hover {
    transform: translateY(-50%) scale(1.02);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  }

  .gantt-task-progress {
    border-radius: 4px;
    transition: width 0.3s ease;
  }

  @media (max-width: 768px) {
    .gantt-task-column {
      width: 200px;
      min-width: 200px;
    }

    .gantt-date-cell {
      min-width: 35px;
    }
  }
}
