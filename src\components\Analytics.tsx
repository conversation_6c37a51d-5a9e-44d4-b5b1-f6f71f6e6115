import type { Task } from '../types.js';

interface AnalyticsProps {
  tasks: Task[];
}

export default function Analytics({ tasks }: AnalyticsProps) {
  const completionRate = tasks.length > 0 ? Math.round((tasks.filter(t => t.completed).length / tasks.length) * 100) : 0;
  
  return (
    <div className="analytics">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-semibold text-primary mb-3">Analytics</h1>
        <p className="text-lg text-secondary">
          Track your productivity and task completion trends.
        </p>
      </div>

      {/* Basic Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="stats-card">
          <div className="flex flex-col items-center text-center">
            <div className="w-12 h-12 rounded-full bg-primary-light flex items-center justify-center mb-3">
              <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <p className="text-2xl font-semibold text-primary mb-1">{completionRate}%</p>
            <p className="text-sm text-secondary">Completion Rate</p>
          </div>
        </div>

        <div className="stats-card">
          <div className="flex flex-col items-center text-center">
            <div className="w-12 h-12 rounded-full bg-success-light flex items-center justify-center mb-3">
              <svg className="w-6 h-6 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
            </div>
            <p className="text-2xl font-semibold text-success mb-1">{tasks.length}</p>
            <p className="text-sm text-secondary">Total Tasks</p>
          </div>
        </div>

        <div className="stats-card">
          <div className="flex flex-col items-center text-center">
            <div className="w-12 h-12 rounded-full bg-warning-light flex items-center justify-center mb-3">
              <svg className="w-6 h-6 text-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <p className="text-2xl font-semibold text-warning mb-1">{tasks.filter(t => t.completed).length}</p>
            <p className="text-sm text-secondary">Completed</p>
          </div>
        </div>
      </div>

      {/* Available Analytics Tools */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Burndown Charts Card */}
        <div className="card p-6">
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 rounded-full bg-primary-light flex items-center justify-center mr-4">
              <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-primary">Burndown Charts</h3>
              <p className="text-sm text-secondary">Track project progress over time</p>
            </div>
          </div>
          <p className="text-secondary mb-4">
            Visualize task completion trends and project velocity with interactive burndown charts.
            Monitor ideal vs actual progress to stay on track.
          </p>
          <button
            onClick={() => window.location.hash = '#burndown'}
            className="btn btn-primary w-full"
          >
            View Burndown Charts
          </button>
        </div>

        {/* Gantt Charts Card */}
        <div className="card p-6">
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 rounded-full bg-success-light flex items-center justify-center mr-4">
              <svg className="w-6 h-6 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-primary">Gantt Charts</h3>
              <p className="text-sm text-secondary">Project timeline visualization</p>
            </div>
          </div>
          <p className="text-secondary mb-4">
            View project timelines and task dependencies with interactive Gantt charts.
            Plan and schedule your work effectively.
          </p>
          <button
            onClick={() => window.location.hash = '#gantt'}
            className="btn btn-secondary w-full"
          >
            View Gantt Charts
          </button>
        </div>

        {/* Calendar View Card */}
        <div className="card p-6">
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 rounded-full bg-warning-light flex items-center justify-center mr-4">
              <svg className="w-6 h-6 text-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-primary">Calendar View</h3>
              <p className="text-sm text-secondary">See tasks in calendar format</p>
            </div>
          </div>
          <p className="text-secondary mb-4">
            Organize and visualize your tasks by date with an intuitive calendar interface.
            Switch between month and week views for better planning.
          </p>
          <button
            onClick={() => window.location.hash = '#calendar'}
            className="btn btn-warning w-full"
          >
            View Calendar
          </button>
        </div>
      </div>

      {/* Coming Soon Features */}
      <div className="card p-6 mt-6">
        <h3 className="text-lg font-semibold text-primary mb-4">Coming Soon</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="flex items-center p-3 bg-surface-tertiary rounded-lg">
            <svg className="w-5 h-5 text-muted mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-sm text-secondary">Time Tracking Reports</span>
          </div>
          <div className="flex items-center p-3 bg-surface-tertiary rounded-lg">
            <svg className="w-5 h-5 text-muted mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <span className="text-sm text-secondary">Productivity Metrics</span>
          </div>
          <div className="flex items-center p-3 bg-surface-tertiary rounded-lg">
            <svg className="w-5 h-5 text-muted mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <span className="text-sm text-secondary">Custom Reports</span>
          </div>
        </div>
      </div>
    </div>
  );
}
