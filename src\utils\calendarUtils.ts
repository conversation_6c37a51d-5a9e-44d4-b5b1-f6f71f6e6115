import { 
  format, 
  startOfMonth, 
  endOfMonth, 
  startOfWeek, 
  endOfWeek, 
  eachDayOfInterval, 
  isSameMonth, 
  isSameDay, 
  isToday, 
  addMonths, 
  subMonths,
  parseISO,
  isWithinInterval,
  startOfDay,
  endOfDay
} from 'date-fns';
import type { Task, Project } from '../types.js';

export interface CalendarDay {
  date: Date;
  isCurrentMonth: boolean;
  isToday: boolean;
  tasks: Task[];
}

export interface CalendarWeek {
  days: CalendarDay[];
}

export interface CalendarMonth {
  year: number;
  month: number;
  monthName: string;
  weeks: CalendarWeek[];
  totalTasks: number;
}

export interface CalendarStats {
  totalTasks: number;
  completedTasks: number;
  overdueTasks: number;
  upcomingTasks: number;
}

/**
 * Generate calendar data for a given month and year
 */
export function generateCalendarMonth(
  year: number,
  month: number,
  tasks: Task[],
  projectFilter?: number
): CalendarMonth {
  const monthStart = startOfMonth(new Date(year, month));
  const monthEnd = endOfMonth(monthStart);
  const calendarStart = startOfWeek(monthStart);
  const calendarEnd = endOfWeek(monthEnd);

  // Filter tasks by project if specified
  const filteredTasks = projectFilter 
    ? tasks.filter(task => task.projectId === projectFilter)
    : tasks;

  // Get all days in the calendar view (including previous/next month days)
  const calendarDays = eachDayOfInterval({ start: calendarStart, end: calendarEnd });

  // Group tasks by date
  const tasksByDate = new Map<string, Task[]>();
  
  filteredTasks.forEach(task => {
    if (task.dueDate) {
      const taskDate = parseISO(task.dueDate);
      const dateKey = format(taskDate, 'yyyy-MM-dd');
      
      if (!tasksByDate.has(dateKey)) {
        tasksByDate.set(dateKey, []);
      }
      tasksByDate.get(dateKey)!.push(task);
    }
  });

  // Create calendar structure
  const weeks: CalendarWeek[] = [];
  let currentWeek: CalendarDay[] = [];

  calendarDays.forEach((day, index) => {
    const dateKey = format(day, 'yyyy-MM-dd');
    const dayTasks = tasksByDate.get(dateKey) || [];

    const calendarDay: CalendarDay = {
      date: day,
      isCurrentMonth: isSameMonth(day, monthStart),
      isToday: isToday(day),
      tasks: dayTasks
    };

    currentWeek.push(calendarDay);

    // Complete week (7 days) or last day
    if (currentWeek.length === 7 || index === calendarDays.length - 1) {
      weeks.push({ days: [...currentWeek] });
      currentWeek = [];
    }
  });

  const totalTasks = filteredTasks.filter(task => 
    task.dueDate && 
    isWithinInterval(parseISO(task.dueDate), { start: monthStart, end: monthEnd })
  ).length;

  return {
    year,
    month,
    monthName: format(monthStart, 'MMMM yyyy'),
    weeks,
    totalTasks
  };
}

/**
 * Generate calendar data for a specific week
 */
export function generateCalendarWeek(
  date: Date,
  tasks: Task[],
  projectFilter?: number
): CalendarWeek {
  const weekStart = startOfWeek(date);
  const weekEnd = endOfWeek(date);

  // Filter tasks by project if specified
  const filteredTasks = projectFilter 
    ? tasks.filter(task => task.projectId === projectFilter)
    : tasks;

  // Get all days in the week
  const weekDays = eachDayOfInterval({ start: weekStart, end: weekEnd });

  // Group tasks by date
  const tasksByDate = new Map<string, Task[]>();
  
  filteredTasks.forEach(task => {
    if (task.dueDate) {
      const taskDate = parseISO(task.dueDate);
      const dateKey = format(taskDate, 'yyyy-MM-dd');
      
      if (!tasksByDate.has(dateKey)) {
        tasksByDate.set(dateKey, []);
      }
      tasksByDate.get(dateKey)!.push(task);
    }
  });

  const days: CalendarDay[] = weekDays.map(day => {
    const dateKey = format(day, 'yyyy-MM-dd');
    const dayTasks = tasksByDate.get(dateKey) || [];

    return {
      date: day,
      isCurrentMonth: true, // All days in week view are considered "current"
      isToday: isToday(day),
      tasks: dayTasks
    };
  });

  return { days };
}

/**
 * Calculate calendar statistics for a given time period
 */
export function calculateCalendarStats(
  tasks: Task[],
  startDate: Date,
  endDate: Date,
  projectFilter?: number
): CalendarStats {
  const filteredTasks = projectFilter 
    ? tasks.filter(task => task.projectId === projectFilter)
    : tasks;

  const periodTasks = filteredTasks.filter(task => {
    if (!task.dueDate) return false;
    const taskDate = parseISO(task.dueDate);
    return isWithinInterval(taskDate, { start: startDate, end: endDate });
  });

  const now = new Date();
  const totalTasks = periodTasks.length;
  const completedTasks = periodTasks.filter(task => task.completed).length;
  const overdueTasks = periodTasks.filter(task => 
    !task.completed && parseISO(task.dueDate!) < now
  ).length;
  const upcomingTasks = periodTasks.filter(task => 
    !task.completed && parseISO(task.dueDate!) >= now
  ).length;

  return {
    totalTasks,
    completedTasks,
    overdueTasks,
    upcomingTasks
  };
}

/**
 * Get tasks for a specific date
 */
export function getTasksForDate(tasks: Task[], date: Date): Task[] {
  const targetDate = format(date, 'yyyy-MM-dd');
  
  return tasks.filter(task => {
    if (!task.dueDate) return false;
    const taskDate = format(parseISO(task.dueDate), 'yyyy-MM-dd');
    return taskDate === targetDate;
  });
}

/**
 * Navigation helpers
 */
export function getNextMonth(year: number, month: number): { year: number; month: number } {
  const nextMonth = addMonths(new Date(year, month), 1);
  return {
    year: nextMonth.getFullYear(),
    month: nextMonth.getMonth()
  };
}

export function getPreviousMonth(year: number, month: number): { year: number; month: number } {
  const prevMonth = subMonths(new Date(year, month), 1);
  return {
    year: prevMonth.getFullYear(),
    month: prevMonth.getMonth()
  };
}

/**
 * Format helpers
 */
export function formatCalendarDate(date: Date): string {
  return format(date, 'd');
}

export function formatCalendarMonth(date: Date): string {
  return format(date, 'MMMM yyyy');
}

export function formatTaskTime(task: Task): string {
  if (!task.dueDate) return '';
  const date = parseISO(task.dueDate);
  return format(date, 'MMM d');
}
