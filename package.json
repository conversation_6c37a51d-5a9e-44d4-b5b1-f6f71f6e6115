{"name": "locumlink2", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:client\"", "dev:client": "vite", "dev:server": "nodemon --exec \"tsx server/index.ts\"", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "db:seed": "tsx prisma/seed.ts", "db:studio": "npx prisma studio"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@prisma/client": "^6.9.0", "@types/multer": "^1.4.13", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "express": "^5.1.0", "form-data": "^4.0.3", "multer": "^2.0.1", "prisma": "^6.9.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/postcss": "^4.1.8", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "concurrently": "^9.1.2", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "nodemon": "^3.1.10", "postcss": "^8.5.4", "tailwindcss": "^4.1.8", "ts-node": "^10.9.2", "tsx": "^4.19.4", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}