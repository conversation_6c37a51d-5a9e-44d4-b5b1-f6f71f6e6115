import { useState, useEffect } from 'react';
import type { Task, User, Project, TaskTemplate, TaskAttachment, CreateTaskData, UpdateTaskData, CreateProjectData, UpdateProjectData, CreateTaskTemplateData, UpdateTaskTemplateData } from './types.js';
import { Priority, TaskStatus } from './types.js';
import { tasksApi, usersApi, projectsApi, taskTemplatesApi, attachmentsApi } from './api';
import TaskCard from './components/TaskCard';
import TaskForm from './components/TaskForm';
import KanbanBoard from './components/KanbanBoard';
import Layout from './components/Layout';
import Dashboard from './components/Dashboard';
import Analytics from './components/Analytics';
import Settings from './components/Settings';
import Projects from './components/Projects';
import ProjectDetail from './components/ProjectDetail';
import ProjectForm from './components/ProjectForm';
import TaskTemplates from './components/TaskTemplates';
import TaskTemplateForm from './components/TaskTemplateForm';
import GanttChart from './components/GanttChart';

function App() {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [templates, setTemplates] = useState<TaskTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [showProjectForm, setShowProjectForm] = useState(false);
  const [showTemplateForm, setShowTemplateForm] = useState(false);
  const [editingTask, setEditingTask] = useState<Task | undefined>();
  const [editingProject, setEditingProject] = useState<Project | undefined>();
  const [editingTemplate, setEditingTemplate] = useState<TaskTemplate | undefined>();
  const [selectedProject, setSelectedProject] = useState<Project | undefined>();
  const [filter, setFilter] = useState<'all' | 'completed' | 'pending'>('all');
  const [priorityFilter, setPriorityFilter] = useState<Priority | 'all'>('all');
  const [view, setView] = useState<'list' | 'kanban'>('list');
  const [currentView, setCurrentView] = useState<string>('dashboard');
  const [sidebarCollapsed, setSidebarCollapsed] = useState<boolean>(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [tasksData, usersData, projectsData, templatesData] = await Promise.all([
        tasksApi.getAll(),
        usersApi.getAll(),
        projectsApi.getAll(),
        taskTemplatesApi.getAll(),
      ]);

      // Add fallback status for tasks without status field
      const tasksWithStatus = tasksData.map(task => ({
        ...task,
        status: task.status || (task.completed ? TaskStatus.DONE : TaskStatus.TODO)
      }));

      setTasks(tasksWithStatus);
      setUsers(usersData);
      setProjects(projectsData);
      setTemplates(templatesData);
      setError(null);
    } catch (err) {
      setError('Failed to load data. Make sure the server is running.');
      console.error('Error loading data:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTask = async (taskData: CreateTaskData) => {
    try {
      const newTask = await tasksApi.create(taskData);
      setTasks([newTask, ...tasks]);
      setShowForm(false);
    } catch (err) {
      setError('Failed to create task');
      console.error('Error creating task:', err);
    }
  };

  const handleUpdateTask = async (taskData: UpdateTaskData) => {
    if (!editingTask) return;
    
    try {
      const updatedTask = await tasksApi.update(editingTask.id, taskData);
      setTasks(tasks.map(task => task.id === editingTask.id ? updatedTask : task));
      setEditingTask(undefined);
      setShowForm(false);
    } catch (err) {
      setError('Failed to update task');
      console.error('Error updating task:', err);
    }
  };

  const handleToggleComplete = async (id: number, completed: boolean) => {
    try {
      const updatedTask = await tasksApi.update(id, { completed });
      setTasks(tasks.map(task => task.id === id ? updatedTask : task));
    } catch (err) {
      setError('Failed to update task');
      console.error('Error updating task:', err);
    }
  };

  const handleUpdateTaskStatus = async (taskId: number, newStatus: TaskStatus) => {
    try {
      const completed = newStatus === TaskStatus.DONE;
      const updatedTask = await tasksApi.update(taskId, { status: newStatus, completed });

      // Ensure the updated task has the status field
      const taskWithStatus = {
        ...updatedTask,
        status: updatedTask.status || newStatus
      };

      setTasks(tasks.map(task => task.id === taskId ? taskWithStatus : task));
    } catch (err) {
      setError('Failed to update task status');
      console.error('Error updating task status:', err);
    }
  };

  const handleDeleteTask = async (id: number) => {
    if (!confirm('Are you sure you want to delete this task?')) return;
    
    try {
      await tasksApi.delete(id);
      setTasks(tasks.filter(task => task.id !== id));
    } catch (err) {
      setError('Failed to delete task');
      console.error('Error deleting task:', err);
    }
  };

  const handleEditTask = (task: Task) => {
    setEditingTask(task);
    setShowForm(true);
  };

  const handleCloseForm = () => {
    setShowForm(false);
    setEditingTask(undefined);
  };

  // Subtask management functions
  const handleCreateSubtask = async (data: CreateTaskData) => {
    try {
      const newSubtask = await tasksApi.create(data);
      setTasks([...tasks, newSubtask]);

      // Update parent task to include the new subtask
      if (data.parentTaskId) {
        setTasks(prevTasks =>
          prevTasks.map(task =>
            task.id === data.parentTaskId
              ? { ...task, subtasks: [...(task.subtasks || []), newSubtask] }
              : task
          )
        );
      }
    } catch (err) {
      setError('Failed to create subtask');
      console.error('Error creating subtask:', err);
    }
  };

  const handleUpdateSubtask = async (id: number, data: Partial<Task>) => {
    try {
      const updatedSubtask = await tasksApi.update(id, data);
      setTasks(prevTasks =>
        prevTasks.map(task => {
          if (task.id === id) {
            return updatedSubtask;
          }
          // Update subtask in parent task's subtasks array
          if (task.subtasks) {
            return {
              ...task,
              subtasks: task.subtasks.map(subtask =>
                subtask.id === id ? updatedSubtask : subtask
              )
            };
          }
          return task;
        })
      );
    } catch (err) {
      setError('Failed to update subtask');
      console.error('Error updating subtask:', err);
    }
  };

  const handleDeleteSubtask = async (id: number) => {
    if (!confirm('Are you sure you want to delete this subtask?')) return;

    try {
      await tasksApi.delete(id);
      setTasks(prevTasks =>
        prevTasks
          .filter(task => task.id !== id) // Remove from main tasks list
          .map(task => ({
            ...task,
            subtasks: task.subtasks?.filter(subtask => subtask.id !== id) || []
          })) // Remove from parent's subtasks array
      );
    } catch (err) {
      setError('Failed to delete subtask');
      console.error('Error deleting subtask:', err);
    }
  };

  const handleReorderSubtasks = async (parentId: number, subtaskIds: number[]) => {
    try {
      const updatedParent = await tasksApi.reorderSubtasks(parentId, subtaskIds);
      setTasks(prevTasks =>
        prevTasks.map(task =>
          task.id === parentId ? updatedParent : task
        )
      );
    } catch (err) {
      setError('Failed to reorder subtasks');
      console.error('Error reordering subtasks:', err);
    }
  };

  // Attachment management functions
  const handleUploadAttachment = async (taskId: number, attachment: TaskAttachment) => {
    setTasks(prevTasks =>
      prevTasks.map(task => {
        if (task.id === taskId) {
          return {
            ...task,
            attachments: [...(task.attachments || []), attachment]
          };
        }
        return task;
      })
    );
  };

  const handleDeleteAttachment = async (attachmentId: number) => {
    try {
      await attachmentsApi.delete(attachmentId);
      setTasks(prevTasks =>
        prevTasks.map(task => ({
          ...task,
          attachments: task.attachments?.filter(att => att.id !== attachmentId) || []
        }))
      );
    } catch (err) {
      setError('Failed to delete attachment');
      console.error('Error deleting attachment:', err);
    }
  };

  const handleViewChange = (newView: string) => {
    setCurrentView(newView);
    // Map sidebar navigation to internal view states
    if (newView === 'list' || newView === 'kanban') {
      setView(newView);
    }
  };

  const handleToggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  // Project management functions
  const handleCreateProject = async (projectData: CreateProjectData) => {
    try {
      const newProject = await projectsApi.create(projectData);
      setProjects([newProject, ...projects]);
      setShowProjectForm(false);
    } catch (err) {
      setError('Failed to create project');
      console.error('Error creating project:', err);
    }
  };

  const handleUpdateProject = async (projectData: UpdateProjectData) => {
    if (!editingProject) return;

    try {
      const updatedProject = await projectsApi.update(editingProject.id, projectData);
      setProjects(projects.map(project => project.id === editingProject.id ? updatedProject : project));
      setEditingProject(undefined);
      setShowProjectForm(false);

      // Update selected project if it's the one being edited
      if (selectedProject && selectedProject.id === editingProject.id) {
        setSelectedProject(updatedProject);
      }
    } catch (err) {
      setError('Failed to update project');
      console.error('Error updating project:', err);
    }
  };

  const handleDeleteProject = async (id: number) => {
    try {
      await projectsApi.delete(id);
      setProjects(projects.filter(project => project.id !== id));

      // Clear selected project if it's the one being deleted
      if (selectedProject && selectedProject.id === id) {
        setSelectedProject(undefined);
        setCurrentView('projects');
      }
    } catch (err) {
      setError('Failed to delete project');
      console.error('Error deleting project:', err);
    }
  };

  const handleEditProject = (project: Project) => {
    setEditingProject(project);
    setShowProjectForm(true);
  };

  const handleViewProject = (project: Project) => {
    setSelectedProject(project);
    setCurrentView('project-detail');
  };

  const handleCloseProjectForm = () => {
    setShowProjectForm(false);
    setEditingProject(undefined);
  };

  // Task template management functions
  const handleCreateTemplate = async (templateData: CreateTaskTemplateData) => {
    try {
      const newTemplate = await taskTemplatesApi.create(templateData);
      setTemplates([newTemplate, ...templates]);
      setShowTemplateForm(false);
    } catch (err) {
      setError('Failed to create task template');
      console.error('Error creating task template:', err);
    }
  };

  const handleUpdateTemplate = async (templateData: UpdateTaskTemplateData) => {
    if (!editingTemplate) return;

    try {
      const updatedTemplate = await taskTemplatesApi.update(editingTemplate.id, templateData);
      setTemplates(templates.map(template => template.id === editingTemplate.id ? updatedTemplate : template));
      setEditingTemplate(undefined);
      setShowTemplateForm(false);
    } catch (err) {
      setError('Failed to update task template');
      console.error('Error updating task template:', err);
    }
  };

  const handleDeleteTemplate = async (id: number) => {
    try {
      await taskTemplatesApi.delete(id);
      setTemplates(templates.filter(template => template.id !== id));
    } catch (err) {
      setError('Failed to delete task template');
      console.error('Error deleting task template:', err);
    }
  };

  const handleEditTemplate = (template: TaskTemplate) => {
    setEditingTemplate(template);
    setShowTemplateForm(true);
  };

  const handleUseTemplate = (template: TaskTemplate) => {
    // This will be handled by the TaskForm component
    setShowForm(true);
  };

  const handleCloseTemplateForm = () => {
    setShowTemplateForm(false);
    setEditingTemplate(undefined);
  };

  const renderCurrentView = () => {
    switch (currentView) {
      case 'dashboard':
        return <Dashboard tasks={tasks} projects={projects} onCreateTask={() => setShowForm(true)} />;
      case 'analytics':
        return <Analytics tasks={tasks} />;
      case 'settings':
        return <Settings />;
      case 'projects':
        return (
          <Projects
            projects={projects}
            tasks={tasks}
            onCreateProject={() => setShowProjectForm(true)}
            onEditProject={handleEditProject}
            onDeleteProject={handleDeleteProject}
            onViewProject={handleViewProject}
          />
        );
      case 'project-detail':
        return selectedProject ? (
          <ProjectDetail
            project={selectedProject}
            tasks={tasks}
            onBack={() => setCurrentView('projects')}
            onEditProject={handleEditProject}
            onDeleteProject={handleDeleteProject}
            onCreateTask={() => setShowForm(true)}
            onEditTask={handleEditTask}
            onDeleteTask={handleDeleteTask}
            onToggleComplete={handleToggleComplete}
          />
        ) : (
          <div>Project not found</div>
        );
      case 'templates':
        return (
          <TaskTemplates
            templates={templates}
            onCreateTemplate={() => setShowTemplateForm(true)}
            onEditTemplate={handleEditTemplate}
            onDeleteTemplate={handleDeleteTemplate}
            onUseTemplate={handleUseTemplate}
          />
        );
      case 'gantt':
        return <GanttChart tasks={tasks} projects={projects} />;
      case 'list':
        return (
          <div>
            {/* Header */}
            <div className="mb-8">
              <h1 className="text-3xl font-semibold text-primary mb-3">Tasks</h1>
              <p className="text-lg text-secondary">
                Manage and organize your tasks efficiently
              </p>
            </div>

            {/* Controls */}
            <div className="card p-6 mb-6">
              <div className="flex flex-col space-y-4">
                {/* Create Task Button */}
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-semibold text-primary">Task Management</h2>
                  <button
                    onClick={() => setShowForm(true)}
                    className="btn btn-primary"
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Create New Task
                  </button>
                </div>

                {/* Filters */}
                <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                  <div className="flex-1">
                    <label className="form-label">Filter by Status</label>
                    <select
                      value={filter}
                      onChange={(e) => setFilter(e.target.value as 'all' | 'completed' | 'pending')}
                      className="form-select"
                    >
                      <option value="all">All Tasks</option>
                      <option value="pending">Pending</option>
                      <option value="completed">Completed</option>
                    </select>
                  </div>

                  <div className="flex-1">
                    <label className="form-label">Filter by Priority</label>
                    <select
                      value={priorityFilter}
                      onChange={(e) => setPriorityFilter(e.target.value as Priority | 'all')}
                      className="form-select"
                    >
                      <option value="all">All Priorities</option>
                      <option value={Priority.URGENT}>Urgent</option>
                      <option value={Priority.HIGH}>High</option>
                      <option value={Priority.MEDIUM}>Medium</option>
                      <option value={Priority.LOW}>Low</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            {/* Tasks List */}
            <div className="space-y-3">
              {filteredTasks.length === 0 ? (
                <div className="card p-8 text-center">
                  <div className="w-16 h-16 rounded-full bg-surface-tertiary flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-primary mb-2">
                    No tasks found
                  </h3>
                  <p className="text-sm text-secondary mb-4">
                    {tasks.length === 0
                      ? "Create your first task to get started!"
                      : "Try adjusting your filters to see more tasks."
                    }
                  </p>
                  {tasks.length === 0 && (
                    <button
                      onClick={() => setShowForm(true)}
                      className="btn btn-primary"
                    >
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      Create Your First Task
                    </button>
                  )}
                </div>
              ) : (
                filteredTasks
                  .filter(task => !task.parentTaskId) // Only show parent tasks in main list
                  .map((task) => (
                    <TaskCard
                      key={task.id}
                      task={task}
                      onToggleComplete={handleToggleComplete}
                      onDelete={handleDeleteTask}
                      onEdit={handleEditTask}
                      onCreateSubtask={handleCreateSubtask}
                      onUpdateSubtask={handleUpdateSubtask}
                      onDeleteSubtask={handleDeleteSubtask}
                      onReorderSubtasks={handleReorderSubtasks}
                      onUploadAttachment={handleUploadAttachment}
                      onDeleteAttachment={handleDeleteAttachment}
                      currentUserId={users[0]?.id}
                    />
                  ))
              )}
            </div>
          </div>
        );
      case 'kanban':
        return (
          <div>
            {/* Header */}
            <div className="mb-8">
              <h1 className="text-3xl font-semibold text-primary mb-3">Kanban Board</h1>
              <p className="text-lg text-secondary">
                Visualize your workflow with drag-and-drop task management
              </p>
            </div>

            {/* Create Task Button */}
            <div className="card p-6 mb-6">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-primary">Board Management</h2>
                <button
                  onClick={() => setShowForm(true)}
                  className="btn btn-primary"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Create New Task
                </button>
              </div>
            </div>

            {/* Kanban Board */}
            <KanbanBoard
              tasks={tasks}
              onUpdateTaskStatus={handleUpdateTaskStatus}
              onEdit={handleEditTask}
              onDelete={handleDeleteTask}
            />
          </div>
        );
      default:
        return <Dashboard tasks={tasks} onCreateTask={() => setShowForm(true)} />;
    }
  };

  const filteredTasks = tasks.filter(task => {
    if (filter === 'completed' && !task.completed) return false;
    if (filter === 'pending' && task.completed) return false;
    if (priorityFilter !== 'all' && task.priority !== priorityFilter) return false;
    return true;
  });

  // Filter out subtasks for main task count statistics
  const mainTasks = tasks.filter(task => !task.parentTaskId);

  const taskStats = {
    total: mainTasks.length,
    completed: mainTasks.filter(t => t.completed).length,
    pending: mainTasks.filter(t => !t.completed).length,
    overdue: mainTasks.filter(t =>
      !t.completed &&
      t.dueDate &&
      new Date(t.dueDate) < new Date()
    ).length,
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-surface-secondary">
        <div className="text-center">
          <div className="spinner mx-auto mb-4"></div>
          <p className="text-secondary">Loading your tasks...</p>
        </div>
      </div>
    );
  }

  return (
    <Layout
      currentView={currentView}
      onViewChange={handleViewChange}
      sidebarCollapsed={sidebarCollapsed}
      onToggleSidebar={handleToggleSidebar}
    >

      {/* Error Message */}
      {error && (
        <div className="mb-8 card p-6 border-error bg-error-light">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="w-6 h-6 text-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-3 flex-1">
              <h3 className="text-base font-semibold text-error mb-2">Connection Error</h3>
              <div className="text-sm text-error mb-4">{error}</div>
              <button
                onClick={loadData}
                className="btn btn-secondary text-error border-error"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Render Current View */}
      {renderCurrentView()}



      {/* Task Form Modal */}
      {showForm && (
        <TaskForm
          users={users}
          projects={projects}
          templates={templates}
          task={editingTask}
          defaultProjectId={selectedProject?.id}
          onSubmit={editingTask ? handleUpdateTask : handleCreateTask}
          onCancel={handleCloseForm}
        />
      )}

      {/* Project Form Modal */}
      {showProjectForm && (
        <ProjectForm
          users={users}
          project={editingProject}
          onSubmit={editingProject ? handleUpdateProject : handleCreateProject}
          onCancel={handleCloseProjectForm}
        />
      )}

      {/* Task Template Form Modal */}
      {showTemplateForm && (
        <TaskTemplateForm
          users={users}
          template={editingTemplate}
          onSubmit={editingTemplate ? handleUpdateTemplate : handleCreateTemplate}
          onCancel={handleCloseTemplateForm}
        />
      )}
    </Layout>
  );
}

export default App;
