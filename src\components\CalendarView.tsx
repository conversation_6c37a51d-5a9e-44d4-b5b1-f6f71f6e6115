import { useState, useMemo } from 'react';
import { format } from 'date-fns';
import type { Task, Project } from '../types.js';
import {
  generateCalendarMonth,
  generateCalendarWeek,
  calculateCalendarStats,
  getNextMonth,
  getPreviousMonth,
  formatCalendarDate,
  type CalendarDay
} from '../utils/calendarUtils.js';

interface CalendarViewProps {
  tasks: Task[];
  projects: Project[];
  onCreateTask?: (date?: Date) => void;
  onEditTask?: (task: Task) => void;
}

export default function CalendarView({ tasks, projects, onCreateTask, onEditTask }: CalendarViewProps) {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewMode, setViewMode] = useState<'month' | 'week'>('month');
  const [selectedProject, setSelectedProject] = useState<number | 'all'>('all');
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);

  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth();

  // Generate calendar data
  const calendarData = useMemo(() => {
    const projectId = selectedProject === 'all' ? undefined : selectedProject;
    
    if (viewMode === 'month') {
      return generateCalendarMonth(currentYear, currentMonth, tasks, projectId);
    } else {
      return generateCalendarWeek(currentDate, tasks, projectId);
    }
  }, [currentYear, currentMonth, currentDate, viewMode, tasks, selectedProject]);

  // Calculate statistics
  const stats = useMemo(() => {
    const projectId = selectedProject === 'all' ? undefined : selectedProject;
    const startDate = viewMode === 'month' 
      ? new Date(currentYear, currentMonth, 1)
      : new Date(currentDate);
    const endDate = viewMode === 'month'
      ? new Date(currentYear, currentMonth + 1, 0)
      : new Date(currentDate);
    
    return calculateCalendarStats(tasks, startDate, endDate, projectId);
  }, [tasks, currentYear, currentMonth, currentDate, viewMode, selectedProject]);

  const handlePreviousPeriod = () => {
    if (viewMode === 'month') {
      const { year, month } = getPreviousMonth(currentYear, currentMonth);
      setCurrentDate(new Date(year, month));
    } else {
      const newDate = new Date(currentDate);
      newDate.setDate(newDate.getDate() - 7);
      setCurrentDate(newDate);
    }
  };

  const handleNextPeriod = () => {
    if (viewMode === 'month') {
      const { year, month } = getNextMonth(currentYear, currentMonth);
      setCurrentDate(new Date(year, month));
    } else {
      const newDate = new Date(currentDate);
      newDate.setDate(newDate.getDate() + 7);
      setCurrentDate(newDate);
    }
  };

  const handleToday = () => {
    setCurrentDate(new Date());
  };

  const handleDateClick = (day: CalendarDay) => {
    setSelectedDate(day.date);
    if (onCreateTask) {
      onCreateTask(day.date);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT': return 'bg-error text-white';
      case 'HIGH': return 'bg-warning text-white';
      case 'MEDIUM': return 'bg-primary text-white';
      case 'LOW': return 'bg-success text-white';
      default: return 'bg-muted text-primary';
    }
  };

  const renderCalendarHeader = () => (
    <div className="flex items-center justify-between mb-6">
      <div className="flex items-center space-x-4">
        <h2 className="text-2xl font-semibold text-primary">
          {viewMode === 'month' 
            ? format(currentDate, 'MMMM yyyy')
            : `Week of ${format(currentDate, 'MMM d, yyyy')}`
          }
        </h2>
        <div className="flex items-center space-x-2">
          <button
            onClick={handlePreviousPeriod}
            className="btn btn-secondary btn-sm"
            title={`Previous ${viewMode}`}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <button
            onClick={handleToday}
            className="btn btn-secondary btn-sm"
          >
            Today
          </button>
          <button
            onClick={handleNextPeriod}
            className="btn btn-secondary btn-sm"
            title={`Next ${viewMode}`}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </div>

      <div className="flex items-center space-x-4">
        {/* View Mode Toggle */}
        <div className="flex items-center bg-surface-tertiary rounded-lg p-1">
          <button
            onClick={() => setViewMode('month')}
            className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
              viewMode === 'month' 
                ? 'bg-primary text-white' 
                : 'text-secondary hover:text-primary'
            }`}
          >
            Month
          </button>
          <button
            onClick={() => setViewMode('week')}
            className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
              viewMode === 'week' 
                ? 'bg-primary text-white' 
                : 'text-secondary hover:text-primary'
            }`}
          >
            Week
          </button>
        </div>

        {/* Project Filter */}
        <select
          value={selectedProject}
          onChange={(e) => setSelectedProject(e.target.value === 'all' ? 'all' : parseInt(e.target.value))}
          className="input-field"
        >
          <option value="all">All Projects</option>
          {projects.map((project) => (
            <option key={project.id} value={project.id}>
              {project.name}
            </option>
          ))}
        </select>
      </div>
    </div>
  );

  const renderDayHeader = () => (
    <div className="grid grid-cols-7 gap-1 mb-2">
      {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
        <div key={day} className="p-2 text-center text-sm font-medium text-secondary">
          {day}
        </div>
      ))}
    </div>
  );

  const renderCalendarDay = (day: CalendarDay) => {
    const isSelected = selectedDate && format(day.date, 'yyyy-MM-dd') === format(selectedDate, 'yyyy-MM-dd');
    
    return (
      <div
        key={format(day.date, 'yyyy-MM-dd')}
        onClick={() => handleDateClick(day)}
        className={`
          calendar-day min-h-[120px] p-2 border border-border rounded-lg cursor-pointer
          transition-all duration-200 hover:bg-surface-secondary
          ${!day.isCurrentMonth ? 'opacity-50' : ''}
          ${day.isToday ? 'bg-primary-light border-primary' : 'bg-surface'}
          ${isSelected ? 'ring-2 ring-primary' : ''}
        `}
      >
        <div className="flex items-center justify-between mb-2">
          <span className={`text-sm font-medium ${
            day.isToday ? 'text-primary' : 
            day.isCurrentMonth ? 'text-primary' : 'text-muted'
          }`}>
            {formatCalendarDate(day.date)}
          </span>
          {day.tasks.length > 0 && (
            <span className="text-xs bg-primary text-white rounded-full px-2 py-1">
              {day.tasks.length}
            </span>
          )}
        </div>

        <div className="space-y-1">
          {day.tasks.slice(0, 3).map((task) => (
            <div
              key={task.id}
              onClick={(e) => {
                e.stopPropagation();
                if (onEditTask) onEditTask(task);
              }}
              className={`
                text-xs p-1 rounded truncate cursor-pointer
                transition-opacity hover:opacity-80
                ${getPriorityColor(task.priority)}
                ${task.completed ? 'opacity-60 line-through' : ''}
              `}
              title={task.title}
            >
              {task.title}
            </div>
          ))}
          {day.tasks.length > 3 && (
            <div className="text-xs text-muted">
              +{day.tasks.length - 3} more
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="calendar-view">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-semibold text-primary mb-3">Calendar View</h1>
        <p className="text-lg text-secondary">
          See your tasks organized by date in a visual calendar format.
        </p>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="stats-card">
          <div className="flex flex-col items-center text-center">
            <div className="w-12 h-12 rounded-full bg-primary-light flex items-center justify-center mb-3">
              <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
            </div>
            <p className="text-2xl font-semibold text-primary mb-1">{stats.totalTasks}</p>
            <p className="text-sm text-secondary">Total Tasks</p>
          </div>
        </div>

        <div className="stats-card">
          <div className="flex flex-col items-center text-center">
            <div className="w-12 h-12 rounded-full bg-success-light flex items-center justify-center mb-3">
              <svg className="w-6 h-6 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <p className="text-2xl font-semibold text-success mb-1">{stats.completedTasks}</p>
            <p className="text-sm text-secondary">Completed</p>
          </div>
        </div>

        <div className="stats-card">
          <div className="flex flex-col items-center text-center">
            <div className="w-12 h-12 rounded-full bg-error-light flex items-center justify-center mb-3">
              <svg className="w-6 h-6 text-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <p className="text-2xl font-semibold text-error mb-1">{stats.overdueTasks}</p>
            <p className="text-sm text-secondary">Overdue</p>
          </div>
        </div>

        <div className="stats-card">
          <div className="flex flex-col items-center text-center">
            <div className="w-12 h-12 rounded-full bg-warning-light flex items-center justify-center mb-3">
              <svg className="w-6 h-6 text-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <p className="text-2xl font-semibold text-warning mb-1">{stats.upcomingTasks}</p>
            <p className="text-sm text-secondary">Upcoming</p>
          </div>
        </div>
      </div>

      {/* Calendar Controls */}
      <div className="card p-6 mb-6">
        {renderCalendarHeader()}
      </div>

      {/* Calendar Grid */}
      <div className="card p-6">
        {renderDayHeader()}
        
        {viewMode === 'month' && 'weeks' in calendarData ? (
          <div className="space-y-1">
            {calendarData.weeks.map((week, weekIndex) => (
              <div key={weekIndex} className="grid grid-cols-7 gap-1">
                {week.days.map(renderCalendarDay)}
              </div>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-7 gap-1">
            {'days' in calendarData && calendarData.days.map(renderCalendarDay)}
          </div>
        )}
      </div>

      {/* Legend */}
      <div className="card p-4 mt-6">
        <h3 className="text-sm font-medium text-primary mb-3">Priority Legend</h3>
        <div className="flex flex-wrap gap-4">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded bg-error"></div>
            <span className="text-sm text-secondary">Urgent</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded bg-warning"></div>
            <span className="text-sm text-secondary">High</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded bg-primary"></div>
            <span className="text-sm text-secondary">Medium</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded bg-success"></div>
            <span className="text-sm text-secondary">Low</span>
          </div>
        </div>
      </div>
    </div>
  );
}
